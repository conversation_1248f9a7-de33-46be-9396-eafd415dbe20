# why does it end like this?

Skipping database generation (no recipes found)
Processing complete!

# fix this

Failed to install browsers
Error: Failed to download webkit, caused by
Error: ERROR: Play<PERSON> does not support webkit on mac14-arm64
    at Object.assert (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/package/lib/utils/utils.js:86:15)
    at Registry.downloadURL (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/package/lib/utils/registry.
js:346:17)
    at Object.downloadBrowserWithProgressBar (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/package/li
b/install/browserFetcher.js:84:26)
    at async validateCache (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/package/lib/install/installe
r.js:129:9)
    at async Object.installBrowsersWithProgressBar (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/pack
age/lib/install/installer.js:80:9)
    at async Object.installBrowsers (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/package/lib/cli/dri
ver.js:93:5)
    at async Command.<anonymous> (/Users/<USER>/Library/Caches/ms-playwright/playwright-rust/driver/package/lib/cli/cli.js
:108:9)
Warning: Failed to extract recipes from JavaScript: Exit with exit status: 1
