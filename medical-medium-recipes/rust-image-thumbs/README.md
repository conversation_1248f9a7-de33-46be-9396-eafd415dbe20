# Rust Image Thumbs

A Rust tool for processing Medical Medium recipe images and creating thumbnails. This is a conversion of the original Emacs Lisp code to Rust.

## Features

- **Image Processing**: Creates thumbnails from recipe images in `own/` and `private/` directories
- **State Management**: Uses JSON file to track file modification times to avoid re-processing unchanged images
- **Recipe Data Extraction**: Uses Playwright to extract recipe data from JavaScript databases in the HTML page
- **Database Generation**: Creates JavaScript files (`dbPublicImages.js`, `dbPrivateImages.js`) with image metadata
- **Notification**: Shows completion notifications via Hammerspoon

## Usage

```bash
# Use default directory (~/Developer/heil-froh/medical-medium-recipes)
cargo run

# Specify custom recipes directory
cargo run -- --recipes-root-dir /path/to/your/recipes

# Show help
cargo run -- --help
```

## Key Changes from Emacs Lisp

1. **CLI Interface**: Uses `clap` for command-line argument parsing
2. **JSON State**: Replaces `mapping.el` with `mapping.json` for state management
3. **Playwright**: Uses `playwright-rust` instead of webdriver for JavaScript execution
4. **Image Processing**: Uses `image` crate instead of ImageMagick for thumbnail generation
5. **Error Handling**: Graceful handling of corrupted images and browser installation issues

## Notes

- The Playwright browser installation may fail on some macOS architectures (mac14-arm64). In this case, the tool will skip recipe extraction and only process images.
- Corrupted JPEG files are handled gracefully with warning messages.
- The tool creates thumbnails with a maximum size of 450x450 pixels and 72% JPEG quality.
