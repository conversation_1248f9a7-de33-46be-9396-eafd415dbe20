use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use std::fs;
use std::path::Path;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileState {
    pub path: String,
    pub modified_time: DateTime<Utc>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct StateManager {
    pub files: Vec<FileState>,
}

impl StateManager {
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        if path.as_ref().exists() {
            let content = fs::read_to_string(path)?;
            let state: StateManager = serde_json::from_str(&content)?;
            Ok(state)
        } else {
            Ok(StateManager::default())
        }
    }

    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)?;
        fs::write(path, content)?;
        Ok(())
    }

    pub fn get_file_state(&self, path: &str) -> Option<&FileState> {
        self.files.iter().find(|f| f.path == path)
    }

    pub fn update_file_state(&mut self, path: String, modified_time: DateTime<Utc>) {
        if let Some(existing) = self.files.iter_mut().find(|f| f.path == path) {
            existing.modified_time = modified_time;
        } else {
            self.files.push(FileState {
                path,
                modified_time,
            });
        }
    }

    pub fn should_process_file(&self, path: &str, current_modified_time: DateTime<Utc>) -> bool {
        match self.get_file_state(path) {
            Some(state) => state.modified_time != current_modified_time,
            None => true, // File not in state, should process
        }
    }
}
