use anyhow::Result;
use serde_json::{json, Value};
use std::fs;
use std::path::PathBuf;

use crate::image_processor::ImageProcessor;

pub struct DatabaseGenerator {
    recipes_root: PathBuf,
    image_processor: ImageProcessor,
}

#[derive(Debug)]
pub struct ImageRecord {
    pub id: String,
    pub images: Vec<String>,
    pub thumb: Option<String>,
}

impl DatabaseGenerator {
    pub fn new(recipes_root: PathBuf) -> Self {
        let image_processor = ImageProcessor::new(recipes_root.clone());
        Self {
            recipes_root,
            image_processor,
        }
    }

    pub fn create_image_database(&self, recipes: &[Value]) -> Result<()> {
        let mut public_records = Vec::new();
        let mut private_records = Vec::new();

        for recipe in recipes {
            if let Some(english_name) = recipe.get("englishName").and_then(|v| v.as_str()) {
                let raw_id = self.format_recipe_name(english_name);

                // Determine the actual ID to use for images
                let id = if let Some(image) = recipe.get("image").and_then(|v| v.as_str()) {
                    image.trim_end_matches(".jpeg").to_string()
                } else {
                    raw_id.clone()
                };

                // Get public images
                let public_images = self.image_processor.get_images_for_id(&id, false)?;
                let public_thumb = public_images
                    .first()
                    .map(|img| img.replace("bilder/", "bilder/thumbs/"));

                public_records.push(ImageRecord {
                    id: raw_id.clone(),
                    images: public_images,
                    thumb: public_thumb,
                });

                // Get private images
                let private_images = self.image_processor.get_images_for_id(&id, true)?;
                let private_thumb = private_images
                    .first()
                    .map(|img| img.replace("bilder/", "bilder/thumbs/"));

                private_records.push(ImageRecord {
                    id: raw_id,
                    images: private_images,
                    thumb: private_thumb,
                });
            }
        }

        self.write_database_file("js/dbPublicImages.js", &public_records, "dbPublicImages")?;
        self.write_database_file("js/dbPrivateImages.js", &private_records, "dbPrivateImages")?;

        Ok(())
    }

    fn write_database_file(
        &self,
        filename: &str,
        records: &[ImageRecord],
        var_name: &str,
    ) -> Result<()> {
        let file_path = self.recipes_root.join(filename);

        // Remove existing file
        if file_path.exists() {
            fs::remove_file(&file_path)?;
        }

        // Write header
        let header = format!("const {var_name} = [");
        fs::write(&file_path, header)?;

        // Write records
        for record in records {
            let json_record = json!({
                "id": record.id,
                "images": record.images,
                "thumb": record.thumb
            });

            let json_str = serde_json::to_string_pretty(&json_record)?;
            let content = format!("{json_str},");

            // Append to file
            let mut existing_content = fs::read_to_string(&file_path)?;
            existing_content.push_str(&content);
            fs::write(&file_path, existing_content)?;
        }

        // Write footer
        let mut final_content = fs::read_to_string(&file_path)?;
        final_content.push_str("];");
        fs::write(&file_path, final_content)?;

        Ok(())
    }

    fn format_recipe_name(&self, name: &str) -> String {
        // Convert recipe name to the format used for image filenames
        // This is a simplified version - you may need to adjust based on actual naming conventions
        name.to_lowercase()
            .replace(' ', "-")
            .replace('&', "and")
            .chars()
            .filter(|c| c.is_alphanumeric() || *c == '-')
            .collect()
    }
}
