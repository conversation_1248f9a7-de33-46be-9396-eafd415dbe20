use anyhow::{anyhow, Context, Result};
use serde_json::Value;
use std::net::TcpListener;
use std::path::PathBuf;
use std::process::{Child, Command, Stdio};
use std::time::Duration;
use thirtyfour::prelude::*;
use tokio::time::sleep;

pub struct RecipeExtractor {
    recipes_root: PathBuf,
}

impl RecipeExtractor {
    pub fn new(recipes_root: PathBuf) -> Self {
        Self { recipes_root }
    }

    fn find_free_port() -> Result<u16> {
        let listener = TcpListener::bind("127.0.0.1:0")?;
        let port = listener.local_addr()?.port();
        drop(listener);
        Ok(port)
    }

    fn spawn_chromedriver(port: u16) -> Result<Child> {
        let mut cmd = Command::new("chromedriver");
        cmd.arg(format!("--port={port}"))
            .arg("--url-base=/")
            .stdout(Stdio::null())
            .stderr(Stdio::null());
        let child = cmd
            .spawn()
            .map_err(|e| anyhow!("Failed to start chromedriver: {}", e))?;
        Ok(child)
    }

    pub async fn extract_recipes(&self) -> Result<Vec<Value>> {
        println!("Updating recipes via headless chromedriver...");

        let port = Self::find_free_port()?;
        let mut chromedriver = Self::spawn_chromedriver(port)?;

        // Give chromedriver a moment to start listening
        sleep(Duration::from_millis(500)).await;

        // Configure headless Chrome capabilities
        let mut caps = DesiredCapabilities::chrome();
        caps.add_arg("--headless=new")?;
        caps.add_arg("--disable-gpu")?;
        caps.add_arg("--no-sandbox")?;
        caps.add_arg("--disable-dev-shm-usage")?;

        let webdriver_url = format!("http://127.0.0.1:{port}");
        let driver = WebDriver::new(&webdriver_url, caps).await.map_err(|e| {
            anyhow!(
                "Failed to connect to chromedriver on {}: {}",
                webdriver_url,
                e
            )
        })?;

        // Navigate to the local HTML file
        let index_path = self.recipes_root.join("index.html");
        let file_url = format!("file://{}", index_path.to_string_lossy());
        driver
            .goto(&file_url)
            .await
            .with_context(|| format!("Failed to navigate to {file_url}"))?;

        // Wait a bit for page to load
        sleep(Duration::from_secs(1)).await;

        let mut all_recipes: Vec<Value> = Vec::new();
        let recipe_types = [
            "dbBrainShots",
            "dbShockTherapies",
            "dbMonoCleanses",
            "dbRecipes",
        ];

        let mut group_counts: Vec<(String, usize)> = Vec::new();
        for recipe_type in &recipe_types {
            let js_code = format!("return JSON.stringify({recipe_type})");
            let result = driver
                .execute(&js_code, Vec::<serde_json::Value>::new())
                .await?;
            let json_str: String = result
                .json()
                .as_str()
                .ok_or_else(|| anyhow!("JavaScript did not return string for {}", recipe_type))?
                .to_string();

            let recipes: Vec<Value> = serde_json::from_str(&json_str)
                .with_context(|| format!("Failed to parse JSON for {recipe_type}"))?;
            let count = recipes.len();
            group_counts.push((recipe_type.to_string(), count));
            all_recipes.extend(recipes);
        }

        // Cleanup: close browser and kill chromedriver
        driver.quit().await.ok();
        // Attempt to kill chromedriver if still running
        let _ = chromedriver.kill();

        // Compact summary output
        println!(
            "  dbBrainShots: {}",
            group_counts
                .iter()
                .find(|(n, _)| n == "dbBrainShots")
                .map(|(_, c)| *c)
                .unwrap_or(0)
        );
        println!(
            "  dbShockTherapies: {}",
            group_counts
                .iter()
                .find(|(n, _)| n == "dbShockTherapies")
                .map(|(_, c)| *c)
                .unwrap_or(0)
        );
        println!(
            "  dbMonoCleanses: {}",
            group_counts
                .iter()
                .find(|(n, _)| n == "dbMonoCleanses")
                .map(|(_, c)| *c)
                .unwrap_or(0)
        );
        println!(
            "  dbRecipes: {}",
            group_counts
                .iter()
                .find(|(n, _)| n == "dbRecipes")
                .map(|(_, c)| *c)
                .unwrap_or(0)
        );
        println!("  Total: {}", all_recipes.len());
        Ok(all_recipes)
    }
}
