use anyhow::Result;
use clap::Parser;
use std::path::PathBuf;

mod database_generator;
mod hammerspoon;
mod image_processor;
mod recipe_extractor;
mod state_manager;

use database_generator::DatabaseGenerator;
use hammerspoon::show_hammerspoon_alert;
use image_processor::ImageProcessor;
use recipe_extractor::RecipeExtractor;
use state_manager::StateManager;

#[derive(Parser)]
#[command(name = "rust-image-thumbs")]
#[command(about = "Processes Cooky Booky Medical Medium recipe images to create thumbnails")]
struct Cli {
    /// Root directory for recipes
    #[arg(long, default_value = "~/Developer/heil-froh/medical-medium-recipes")]
    recipes_root_dir: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Expand tilde in path
    let recipes_root = if cli.recipes_root_dir.starts_with('~') {
        let home = dirs::home_dir().expect("Could not find home directory");
        home.join(cli.recipes_root_dir.strip_prefix("~/").unwrap())
    } else {
        PathBuf::from(cli.recipes_root_dir)
    };

    println!("Using recipes root directory: {}", recipes_root.display());

    // Load state from JSON file instead of mapping.el
    let state_file = recipes_root.join("bilder/mapping.json");
    let mut state_manager = StateManager::load_from_file(&state_file)?;

    // Extract recipes from JavaScript
    let extractor = RecipeExtractor::new(recipes_root.clone());
    let recipes = extractor.extract_recipes().await?;

    // Process images and create thumbnails
    let image_processor = ImageProcessor::new(recipes_root.clone());

    println!("Converting all to thumbs...");
    let converted_own = image_processor.process_directory("own", &mut state_manager)?;
    let converted_private = image_processor.process_directory("private", &mut state_manager)?;

    // Save updated state
    state_manager.save_to_file(&state_file)?;

    // Generate image database files
    if !recipes.is_empty() {
        let db_generator = DatabaseGenerator::new(recipes_root);
        db_generator.create_image_database(&recipes)?;
    } else {
        println!("Skipping database generation (no recipes found)");
    }

    // Show completion notification
    let total_converted = converted_own + converted_private;
    show_hammerspoon_alert(&format!(
        "Converted {total_converted} images (own: {converted_own}, private: {converted_private})"
    ));

    println!(
        "Processing complete! Converted {total_converted} images (own: {converted_own}, private: {converted_private})"
    );
    Ok(())
}
