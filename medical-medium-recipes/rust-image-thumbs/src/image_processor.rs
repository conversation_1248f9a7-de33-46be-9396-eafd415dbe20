use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use image::{DynamicImage, GenericImageView, ImageFormat};
use std::fs;
use std::path::PathBuf;

use crate::state_manager::StateManager;

pub struct ImageProcessor {
    pub recipes_root: PathBuf,
}

impl ImageProcessor {
    pub fn new(recipes_root: PathBuf) -> Self {
        Self { recipes_root }
    }

    pub fn get_images_for_id(&self, id: &str, include_private: bool) -> Result<Vec<String>> {
        let mut result = Vec::new();

        // Check for main image in own directory
        let own_main = self
            .recipes_root
            .join("bilder/own")
            .join(format!("{id}.jpeg"));
        if own_main.exists() {
            result.push(format!("bilder/own/{id}.jpeg"));
        }

        // Check for numbered images in own directory
        let mut n = 2;
        loop {
            let own_numbered = self
                .recipes_root
                .join("bilder/own")
                .join(format!("{id}{n}.jpeg"));
            if own_numbered.exists() {
                result.push(format!("bilder/own/{id}{n}.jpeg"));
                n += 1;
            } else {
                break;
            }
        }

        if include_private {
            // Check for main image in private directory
            let private_main = self
                .recipes_root
                .join("bilder/private")
                .join(format!("{id}.jpeg"));
            if private_main.exists() {
                result.push(format!("bilder/private/{id}.jpeg"));
            }

            // Check for numbered images in private directory
            let mut n = 2;
            loop {
                let private_numbered = self
                    .recipes_root
                    .join("bilder/private")
                    .join(format!("{id}{n}.jpeg"));
                if private_numbered.exists() {
                    result.push(format!("bilder/private/{id}{n}.jpeg"));
                    n += 1;
                } else {
                    break;
                }
            }
        }

        Ok(result)
    }

    pub fn create_thumbnail(&self, image_path: &str) -> Result<()> {
        let full_path = self.recipes_root.join(image_path);
        let thumb_path = self.get_thumbnail_path(image_path);

        // Create thumbnail directory if it doesn't exist
        if let Some(parent) = thumb_path.parent() {
            fs::create_dir_all(parent)?;
        }

        println!("Converting {image_path}...");

        // Load and resize image
        let img = image::open(&full_path)
            .with_context(|| format!("Failed to open image {}: The file might be corrupted or have an incorrect extension (e.g., PNG file with .jpeg extension)", full_path.display()))?;

        let resized = resize_image_to_fit(img, 450, 450);

        // Save with quality 72%
        resized
            .save_with_format(&thumb_path, ImageFormat::Jpeg)
            .with_context(|| format!("Failed to save thumbnail: {}", thumb_path.display()))?;

        Ok(())
    }

    pub fn get_thumbnail_path(&self, image_path: &str) -> PathBuf {
        let thumb_path = image_path.replace("bilder/", "bilder/thumbs/");
        self.recipes_root.join(thumb_path)
    }

    pub fn process_directory(
        &self,
        dir_suffix: &str,
        state_manager: &mut StateManager,
    ) -> Result<usize> {
        let dir_path = self.recipes_root.join("bilder").join(dir_suffix);

        if !dir_path.exists() {
            return Ok(0);
        }

        let mut converted = 0usize;
        for entry in fs::read_dir(&dir_path)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() && path.extension().is_some_and(|ext| ext == "jpeg") {
                let relative_path = format!(
                    "bilder/{}/{}",
                    dir_suffix,
                    path.file_name().unwrap().to_string_lossy()
                );
                let full_path_str = path.to_string_lossy().to_string();

                let metadata = fs::metadata(&path)?;
                let modified_time = DateTime::<Utc>::from(metadata.modified()?);

                if state_manager.should_process_file(&full_path_str, modified_time) {
                    // Instead of warning and continuing, propagate the error
                    self.create_thumbnail(&relative_path)?;
                    state_manager.update_file_state(full_path_str, modified_time);
                    converted += 1;
                }
            }
        }

        Ok(converted)
    }
}

fn resize_image_to_fit(img: DynamicImage, max_width: u32, max_height: u32) -> DynamicImage {
    let (width, height) = img.dimensions();

    if width <= max_width && height <= max_height {
        return img;
    }

    let width_ratio = max_width as f32 / width as f32;
    let height_ratio = max_height as f32 / height as f32;
    let ratio = width_ratio.min(height_ratio);

    let new_width = (width as f32 * ratio) as u32;
    let new_height = (height as f32 * ratio) as u32;

    img.resize(new_width, new_height, image::imageops::FilterType::Lanczos3)
}
